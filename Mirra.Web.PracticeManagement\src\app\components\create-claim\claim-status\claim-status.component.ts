import { Component, DebugElement, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ColDef, GridApi, IGetRowsParams, IsRowSelectable } from 'ag-grid-community';
import { NgxSpinnerService } from 'ngx-spinner';
import { distinctUntilChanged, first, Subject, takeUntil } from 'rxjs';
import { DashboardSearchCriteria } from 'src/app/classmodels/ResponseModel/Dashboard/DashboardSearchCriteria';
import { IPACodesModel } from 'src/app/classmodels/ResponseModel/MasterData/ipcode';
import { CLAIM_GRID_DEFCOLUMNS, ShowHideCheckboxClaimsGrid } from 'src/app/common/claim-grid-columns';
import { CLAIM_STATUS, CLAIM_TYPE, COMMON_METHODS, COMMON_VALUES, PREVILEGES, SwalFire } from 'src/app/common/common-static';
import { submitValidateAllFields } from 'src/app/common/form.validators';
import { BillingProviderFinalResult, BillingProviderResult } from 'src/app/models/ClaimForm/Providers/BillingProviderResult';
import { BillingProviderSearchCriteria } from 'src/app/models/ClaimForm/Providers/BillingProviderSearchCriteria';
import { ClaimGridModel } from 'src/app/models/claim-grid-model';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';
import { MemberService } from 'src/app/services/Member/member.service';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { ProviderManagementService } from 'src/app/services/ProviderManagement/provider-management.service';
import { ProviderService } from 'src/app/services/Providers/provider.service';
import { BillingProviderService } from 'src/app/services/billing-provider/billing-provider.service';
import { DispatchClaimService } from 'src/app/services/claim-buckets/dispatch-claim.service';
import { ClaimsTrackingService } from 'src/app/services/claims-tracking/claims-tracking.service';
import { GRID_CONFIG } from 'src/app/shared/config-ag-grid/config-grid';
import { SubjectService } from 'src/app/shared/services/subject.service';
import { ResubmitConfimComponent } from '../../popups/resubmit-confim/resubmit-confim.component';
import { ClaimStatusChangeModel, ClaimStatusModel } from 'src/app/models/ClaimForm/change.claim.status.model';
import { ClaimService } from 'src/app/services/ClaimForm/claim.service';
import * as moment from 'moment';
import { GlobalService } from 'src/app/shared/services/global.service';
import { ExportService } from 'src/app/services/export-service/export.service';
import { Workbook } from 'exceljs';
import * as fs from 'file-saver';
import { ValidationMsgs } from 'src/app/common/common-static';
import { DateFormatter } from 'src/app/shared/functions/dateFormatterFunction';
import { SelectAllComponent } from 'src/app/shared/components/select-all/select-all.component';
import { CustomDateFloatingFilterComponent } from 'src/app/common/custom-date-floating-filter.component';
import { AgGridAngular } from 'ag-grid-angular';
import * as _ from 'lodash';
@Component({
  selector: 'app-claim-status',
  templateUrl: './claim-status.component.html',
  styleUrls: ['./claim-status.component.scss']
})
export class ClaimStatusComponent implements OnInit {
  billingProviderFinalResult: BillingProviderFinalResult[] = [];
  billingProviderResult: BillingProviderResult[] = [];
  @Input() ipaCodeItems: IPACodesModel[];
  @Output() selectedBillingProvider: EventEmitter<BillingProviderResult> = new EventEmitter();
  dashboardSerchCriteria: DashboardSearchCriteria = {};
  selectedGridItems: DashboardSearchCriteria[] = [];
  public rowSelection: 'single' | 'multiple' = 'multiple';
  billingProviderInfo: FormGroup;
  selectedIPACodes: string = '';
  allIPACode: string = '';
  gridOptions: any = JSON.parse(JSON.stringify(GRID_CONFIG.scrollConfigDetails.checkboxScrollGridOptions));
  claimGridModel: ClaimGridModel[] = [];
  claims: ClaimGridModel[] = [];
  columnDefs: ColDef[] = [];
  buttonVisibleType: string = '';
  claimType: any = CLAIM_TYPE;
  claimHeader: string = '';
  selectedRow: DashboardSearchCriteria[] = [];
  claimCount: number = 0;
  currentTabTotalFileCount: number = 0;
  overlayNoRowsTemplate: any = ValidationMsgs.no_data_available;
  overlayLoadingTemplate: string = '<div style="padding: 20px; text-align: center;"><div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; border-top: 3px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite;"></div><div style="margin-top: 10px;">Loading claims...</div></div><style>@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>';

  gridApi!: GridApi<ClaimGridModel>;
  gridDataApi!: GridApi;
  isGridLoading: boolean = false;
  isIgnoreHeaderShow: boolean = false;
  isGenerateButtonShow: boolean = false;
  isMoveToAcceptedButtonShow: boolean = false;
  isResubmitButtonShow: boolean = false;
  isPrivMoveSelectedOpenToAccepted: boolean = false;
  claimListArrayString: string;

  claimsStatusList: ClaimStatusModel;
  isClaimBillingMngmntGenrtEDI837File: boolean = false;
  billerPrivMoveAllOpenToAccepted: boolean = false;
  privMoveAllOpenToAccepted: boolean = false;
  isClaimsBillingManagementResubmission: boolean = false;
  destroy$: Subject<boolean> = new Subject<boolean>();
  @ViewChild('agGrid') agGrid!: AgGridAngular;
  isFilterGrid: boolean = false;
  frameworkComponents: any = {
    selectAllComponent: SelectAllComponent,
    customDateFloatingFilter: CustomDateFloatingFilterComponent,
  };
  isAllSelectedGlobally: boolean = false;
  excludedIds: Set<number> = new Set<number>();
  globalCheckboxSelectedGridItems: DashboardSearchCriteria[] = [];
  isGridSelectedRowCountShow: boolean = false;

  constructor(
    public dailog: MatDialog, private subjectService: SubjectService,
    private providerMgmtService: ProviderManagementService,
    private providerService: ProviderService,
    private memberService: MemberService,
    private claimReportService: ClaimReportServiceService,
    private claimsTrackingService: ClaimsTrackingService,
    private billingProviderService: BillingProviderService,
    private billingForm: FormBuilder,
    private notificationService: NotificationService,
    private dispatchClaimService: DispatchClaimService,
    public dialogRef: MatDialogRef<ClaimStatusComponent>,
    private spinner: NgxSpinnerService,
    private readonly globalService: GlobalService,
    private claimService: ClaimService,
    private readonly exportService: ExportService) {
    this.getPrivileges();
    this.createForm();
    this.subjectRefresh();
    dialogRef.disableClose = true;
  }

  ngOnInit(): void {
    this.gridOptions.getRowStyle = this.changeRowColor;

    // Configure overlay templates
    this.gridOptions.overlayLoadingTemplate = this.overlayLoadingTemplate;
    this.gridOptions.overlayNoRowsTemplate = this.overlayNoRowsTemplate;
  }

  getCurrentTabGridColumnDef(claimTab: string) {
    switch (claimTab) {
      case CLAIM_TYPE.open: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.OPEN_COLUMN_DEFS(this.privMoveAllOpenToAccepted, this.isPrivMoveSelectedOpenToAccepted, this.billerPrivMoveAllOpenToAccepted);
        this.claimHeader = 'Open'
        this.isMoveToAcceptedButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.onHold: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ONHOLD_COLUMN_DEFS;
        this.claimHeader = 'On Hold'
        this.isGridSelectedRowCountShow = false;
        break;
      }
      case CLAIM_TYPE.ntr: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.RESUBMISSION_COLUMN_DEFS;
        this.claimHeader = 'Resubmission'
        this.isGridSelectedRowCountShow = false;
        break;
      }
      case CLAIM_TYPE.accepted: {
        this.rowSelection = 'multiple';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_COLUMN_DEFS(this.isClaimBillingMngmntGenrtEDI837File);
        this.claimHeader = 'Accepted';
        this.isGenerateButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.dispatched: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.DISPATCHED_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);
        this.claimHeader = 'Dispatched';
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.unackByCH: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_CH_COLUMN_DEFS
        this.claimHeader = 'Unacknowledged by CH';
        this.isGridSelectedRowCountShow = false;
        break;
      }
      case CLAIM_TYPE.rejectedByCH: {
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.REJECTED_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission)
        this.claimHeader = 'Rejected By CH';
        this.isIgnoreHeaderShow = true;
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.acknolwedgedByCH: {
        this.claimHeader = 'Acknowledged by CH';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACK_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }

      case CLAIM_TYPE.acceptedByCH: {
        this.claimHeader = 'Accepted By CH';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_BY_CH_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.unAckByPayer: {
        this.claimHeader = 'Unacknowledged by Payer';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.UN_ACK_BY_PAYER_COLUMN_DEFS;
        this.isResubmitButtonShow = false;
        this.isGridSelectedRowCountShow = false;
        break;
      }

      case CLAIM_TYPE.rejectedByPayer: {
        this.claimHeader = 'Rejected By Payer';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.REJECTED_BY_PAYER_COLUMN_DEFS(this.isClaimsBillingManagementResubmission)
        this.isIgnoreHeaderShow = true;
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }

      case CLAIM_TYPE.acknolwedgedByPayer: {
        this.claimHeader = 'Acknowledged by Payer';
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACK_BY_PAYER_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.acceptedByPayer: {
        this.claimHeader = 'Accepted By Payer'
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.ACCEPTED_BY_PAYER(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.pending: {
        this.claimHeader = 'Pending'
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.PENDING_COLUMN_DEFS(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }

      case CLAIM_TYPE.eobReceived: {
        this.claimHeader = 'EOB Received'
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.EOB_RECIEVED(this.isClaimsBillingManagementResubmission)
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }
      case CLAIM_TYPE.deniedByPayer: {
        this.claimHeader = 'Denied By Payer'
        this.columnDefs = CLAIM_GRID_DEFCOLUMNS.DENIED_BY_PAYER(this.isClaimsBillingManagementResubmission);
        this.isResubmitButtonShow = true;
        this.isGridSelectedRowCountShow = true;
        break;
      }

    }
    this.buttonVisibleType = claimTab;
  }

  createForm() {
    this.billingProviderInfo = this.billingForm.group({
      address: new FormControl(''),
      ipaCode: new FormControl(''),
      tax: new FormControl(''),
      name: new FormControl(''),
      npi: new FormControl(''),
    })
    return this.billingProviderInfo;
  }

  get f() { return this.billingProviderInfo.controls; }


  searchBilling() {
    this.ipaCodeItems?.forEach((ipa: IPACodesModel) => {
      this.allIPACode += ipa.ipaCode + ",";
    })
    if (this.billingProviderInfo.invalid) {
      submitValidateAllFields.validateAllFields(this.billingProviderInfo);

      return;
    }
    this.fillBillingProvider();
  }
  fillBillingProvider() {
    this.billingProviderFinalResult = [];
    let billing: BillingProviderSearchCriteria = {
      sortBy: 'ProviderNPI',
      sortOrder: 'ASC',
      limit: 50,
      index: 0,
      providerNPI: this.billingProviderInfo.controls['npi'].value === "" ? null : this.billingProviderInfo.controls['npi'].value,
      firstName: this.billingProviderInfo.controls['name'].value === "" ? null : this.billingProviderInfo.controls['name'].value,
      iPACode: this.selectedIPACodes === "" ? this.allIPACode : this.selectedIPACodes,
      address1: this.billingProviderInfo.controls['address'].value === "" ? null : this.billingProviderInfo.controls['address'].value,
      taxID: this.billingProviderInfo.controls['tax'].value === "" ? null : this.billingProviderInfo.controls['tax'].value,
    }
    this.billingProviderService.fetchBillingProviderResult(billing).subscribe((res: BillingProviderResult[]) => {

      if (res.length > 0) {
        this.billingProviderResult = res;
        res.forEach((e) => {
          e.payToDetails.forEach((p) => {
            let ipaName = '';
            p.ipaDetails.forEach((ipa) => {
              ipaName += ipa.ipaName;
            });
            this.billingProviderFinalResult.push({
              address: p.address1,
              npi: p.payToNPI,
              name: p.paytoName,
              taxId: p.billingProviderTaxonomy,
              ipaName: ipaName,
              taxIdOrSSN: e.taxIdOrSSN,
              VendorID: e.vendorID
            })/**/
          })
        })
      }
    })
  }
  changeRowColor(params: any) {
    if (params.node.rowIndex % 2 === 0) {
      return { 'background-color': '#f1f0f0' };
    } else {
      return { 'background-color': 'white' };
    }
  }

  onGridReady(params: any) {
    this.gridApi = params;
    this.gridDataApi = params.api;

    // Set datasource for infinite scrolling - ag-grid will handle loading overlay automatically
    params.api.setDatasource(this.getDataSource());
  }


  private getDataSource() {
    const dataSource = {
      rowCount: undefined,
      getRows: (params: IGetRowsParams) => {
        // Set loading state to true when starting to fetch data
        this.isGridLoading = true;

        if (this.currentTabTotalFileCount >= params.startRow) {
          if (params.startRow == 0 && Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length == 0) {
            this.isFilterGrid = false;
            this.restFilterModel();
            this.fetchClaimList(params);
          }
          else {
            this.restFilterModel();
            this.getGridFilterModelData(params)
          }
        }
      },
    };
    return { ...dataSource };
  }


  fetchClaimList(params: any) {
    // Let ag-grid handle loading overlay automatically for infinite scrolling
    this.gridPayloadRequest(params);

    const dosFromYear = new Date(this.dashboardSerchCriteria.dOSFrom).getFullYear();
    if (dosFromYear >= 2010 && dosFromYear < new Date().getFullYear() && this.dashboardSerchCriteria.dOSFrom != null) {
      return
    }
    this.claimReportService.getClaimList(this.dashboardSerchCriteria).pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (data.statusCode == 200) {
          if ((!!data.content && (data.content.claimList || []).length > 0)) {
            this.claims = data?.content?.claimList;
            if (this.isFilterGrid) {
              params.successCallback(this.claims, Number(data.content.totalRecordsCount));
            }
            else {
              if (this.currentTabTotalFileCount > 0 && this.claims.length > 0) {
                params.successCallback(this.claims, this.currentTabTotalFileCount);
              }
            }
          }
          else {
            params.successCallback([], (data.content.claimList || []).length);
          }
          if (this.isAllSelectedGlobally) {
            this.gridDataApi?.forEachNode((node) => {
              if (!!node.data && ShowHideCheckboxClaimsGrid.showHideCheckbox(node)) {
                node.setSelected(true);
                node.data.isGloballySelected = true; // Update the data model if needed
                node.data.isSelected = true; // Update the data model if needed
              }
            });
          }
          this.bindDOSDate();
          this.bindReasonForRejctecByCH();
          this.claimGridModel = this.claims;
        } else {
          // API returned error status
          params.successCallback([], 0);
        }

        // Set loading state to false when API call completes
        this.isGridLoading = false;
        // ag-grid will automatically handle hiding the loading overlay
      },
        error => {
          // Set loading state to false on error
          this.isGridLoading = false;
          // Call failCallback to let ag-grid know the request failed
          // ag-grid will automatically handle the loading overlay
          params.failCallback();
        });


  }

  getGridFilterModelData(params: any) {

    // Grid columns search filters without sorting
    if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length == 0) {
      this.gridFilterModel(params);
      this.fetchClaimList(params);
    }
    //Grid columns search filters with sorting
    else if (Object.keys(params.filterModel).length > 0 && Object.keys(params.sortModel).length > 0) {
      this.gridFilterModel(params);
      this.GridSortModel(params);
      this.fetchClaimList(params);
    }
    //Grid columns without search filters with sorting
    else if (Object.keys(params.filterModel).length == 0 && Object.keys(params.sortModel).length > 0) {
      this.GridSortModel(params);
      this.isFilterGrid = false;
      this.fetchClaimList(params);
    }
    else {
      this.fetchClaimList(params);
    }
  }

  gridFilterModel(params: any) {
    if (!!params.filterModel?.claimControlNumber?.filter) { // for grid claimControlNumber column filters.
      this.dashboardSerchCriteria.claimControlNumber = params.filterModel?.claimControlNumber?.filter,
        this.isFilterGrid = true;
    }

    if (!!params.filterModel?.payer?.filter) {
      this.dashboardSerchCriteria.payer = params.filterModel?.payer?.filter,
        this.isFilterGrid = true;
    }
    if (!!params.filterModel?.memberFullName?.filter) {
      this.dashboardSerchCriteria.memFirstName = params.filterModel?.memberFullName?.filter,
        this.isFilterGrid = true;
    }

    if (!!params.filterModel?.dos) {
      this.dashboardSerchCriteria.dOSFrom = moment.utc(params.filterModel?.dos?.dateFrom).toDate()
      this.isFilterGrid = true;
    }
    if (!!params.filterModel?.renderingProviderFullName?.filter) {
      this.dashboardSerchCriteria.renderingProviderFirstName = params.filterModel?.renderingProviderFullName?.filter,
        this.isFilterGrid = true;
    }

    if (!!params.filterModel?.claimAmount?.filter) {
      this.dashboardSerchCriteria.claimAmount = Number(COMMON_METHODS.removeDollarSign(params.filterModel?.claimAmount?.filter))
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.claimType?.filter) {
      this.dashboardSerchCriteria.claimType = params.filterModel?.claimType?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.dateCreated) {
      this.dashboardSerchCriteria.dateCreated = moment.utc(params.filterModel?.dateCreated?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.age?.filter) {
      this.dashboardSerchCriteria.age = params.filterModel?.age?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.createdBy?.filter) {
      this.dashboardSerchCriteria.createdBy = params.filterModel?.createdBy?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.reviewedBy?.filter) {
      this.dashboardSerchCriteria.reviewedBy = params.filterModel?.reviewedBy?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.statusModifiedDate) {
      if (this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.onHold) {
        this.dashboardSerchCriteria.statusModifiedDate = moment.utc(params.filterModel?.statusModifiedDate?.dateFrom).toDate();
        this.isFilterGrid = true;
      }
      else {
        this.dashboardSerchCriteria._999ProcessedOn_CH = moment.utc(params.filterModel?.statusModifiedDate?.dateFrom).toDate();
        this.isFilterGrid = true;
      }
    }

    if (!!params.filterModel?.fileType?.filter) {
      this.dashboardSerchCriteria.fileType = params.filterModel?.fileType?.filter == '999' ? '005010X231A1' : (params.filterModel?.fileType?.filter == '277' ? '005010X214' : params.filterModel?.fileType?.filter)
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.parent_patientctrlno?.filter) {
      this.dashboardSerchCriteria.parent_patientctrlno = params.filterModel?.parent_patientctrlno?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.sentOn) {
      this.dashboardSerchCriteria.sentOn = moment.utc(params.filterModel?.sentOn?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.sentTo?.filter) {
      this.dashboardSerchCriteria.sentTo = params.filterModel?.sentTo?.filter
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?._999ProcessedOn_CH) {
      this.dashboardSerchCriteria._999ProcessedOn_CH = moment.utc(params.filterModel?._999ProcessedOn_CH?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?._277ProcessedOn_CH) {
      this.dashboardSerchCriteria._277ProcessedOn_CH = moment.utc(params.filterModel?._277ProcessedOn_CH?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?._277ProcessedOn_Payer) {
      this.dashboardSerchCriteria._277ProcessedOn_Payer = moment.utc(params.filterModel?._277ProcessedOn_Payer?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?._277PendOnDate) {
      this.dashboardSerchCriteria._277PendOnDate = moment.utc(params.filterModel?._277PendOnDate?.dateFrom).toDate();
      this.isFilterGrid = true;
    }
    if (!!params.filterModel?.paidAmount?.filter) {
      this.dashboardSerchCriteria.paidAmount = Number(COMMON_METHODS.removeDollarSign(params.filterModel?.paidAmount?.filter))
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.paidOn) {
      this.dashboardSerchCriteria.paidOn = moment.utc(params.filterModel?.paidOn?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.acceptedOn) {
      this.dashboardSerchCriteria.acceptedOn = moment.utc(params.filterModel?.acceptedOn?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?._835PaidOnDate) {
      this.dashboardSerchCriteria._835PaidOnDate = moment.utc(params.filterModel?._835PaidOnDate?.dateFrom).toDate();
      this.isFilterGrid = true;
    }

    if (!!params.filterModel?.unackfor?.filter) {
      this.dashboardSerchCriteria.unackfor = params.filterModel?.unackfor?.filter;
      this.isFilterGrid = true;
    }
  }

  restFilterModel() {
    if (!!this.dashboardSerchCriteria.claimControlNumber) {
      this.dashboardSerchCriteria.claimControlNumber = null;
    }
    if (!!this.dashboardSerchCriteria.memFirstName) {
      this.dashboardSerchCriteria.memFirstName = null;
    }
    if (!!this.dashboardSerchCriteria.payer) {
      this.dashboardSerchCriteria.payer = null;
    }

    if (!!this.dashboardSerchCriteria.dOSFrom) {
      this.dashboardSerchCriteria.dOSFrom = null;
    }

    if (!!this.dashboardSerchCriteria.renderingProviderFirstName) {
      this.dashboardSerchCriteria.renderingProviderFirstName = null;
    }

    if (!!this.dashboardSerchCriteria.claimAmount) {
      this.dashboardSerchCriteria.claimAmount = null;
    }

    if (!!this.dashboardSerchCriteria.claimType) {
      this.dashboardSerchCriteria.claimType = null;
    }

    if (!!this.dashboardSerchCriteria.dateCreated) {
      this.dashboardSerchCriteria.dateCreated = null;
    }

    if (!!this.dashboardSerchCriteria.age) {
      this.dashboardSerchCriteria.age = null;
    }

    if (!!this.dashboardSerchCriteria.createdBy) {
      this.dashboardSerchCriteria.createdBy = null;
    }

    if (!!this.dashboardSerchCriteria.reviewedBy) {
      this.dashboardSerchCriteria.reviewedBy = null;
    }

    if (!!this.dashboardSerchCriteria.statusModifiedDate) {
      this.dashboardSerchCriteria.statusModifiedDate = null;
    }

    if (!!this.dashboardSerchCriteria.statusReason) {
      this.dashboardSerchCriteria.statusReason = null;
    }

    if (!!this.dashboardSerchCriteria.parent_patientctrlno) {
      this.dashboardSerchCriteria.parent_patientctrlno = null;
    }

    if (!!this.dashboardSerchCriteria.sentOn) {
      this.dashboardSerchCriteria.sentOn = null;
    }

    if (!!this.dashboardSerchCriteria.sentTo) {
      this.dashboardSerchCriteria.sentTo = null;
    }

    if (!!this.dashboardSerchCriteria._999ProcessedOn_CH) {
      this.dashboardSerchCriteria._999ProcessedOn_CH = null;
    }

    if (!!this.dashboardSerchCriteria._277ProcessedOn_CH) {
      this.dashboardSerchCriteria._277ProcessedOn_CH = null;
    }

    if (!!this.dashboardSerchCriteria._277ProcessedOn_Payer) {
      this.dashboardSerchCriteria._277ProcessedOn_Payer = null;
    }
    // this.isFilterGrid = false;
    if (!!this.dashboardSerchCriteria._277PendOnDate) {
      this.dashboardSerchCriteria._277PendOnDate = null;
    }

    if (!!this.dashboardSerchCriteria.paidAmount) {
      this.dashboardSerchCriteria.paidAmount = null;
    }

    if (!!this.dashboardSerchCriteria.paidOn) {
      this.dashboardSerchCriteria.paidOn = null;
    }

    if (!!this.dashboardSerchCriteria.acceptedOn) {
      this.dashboardSerchCriteria.acceptedOn = null;
    }

    if (!!this.dashboardSerchCriteria._835PaidOnDate) {
      this.dashboardSerchCriteria._835PaidOnDate = null;
    }

    if (!!this.dashboardSerchCriteria.unackfor) {
      this.dashboardSerchCriteria.unackfor = null;
    }

    if (!!this.dashboardSerchCriteria.fileType) {
      this.dashboardSerchCriteria.fileType = null;
    }

  }

  fetchAllClaimsListForGlobalCheckbox() {
    this.spinner.show();
    let allClaimListRequest: DashboardSearchCriteria = this.dashboardSerchCriteria;
    allClaimListRequest.index = null;
    this.claimReportService.getClaimList(allClaimListRequest).pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (data.statusCode == 200) {
          if ((!!data.content && (data.content.claimList || []).length > 0)) {
            let allClaimListResp = data?.content?.claimList;
            allClaimListResp = allClaimListResp.filter(claim => claim.isLocked === false && claim.clearingHouseId !== null);
            this.globalCheckboxSelectedGridItems = allClaimListResp;
            this.spinner.hide();
            this.allClaimListDispatchAlert(`Are you sure you want to generate <b>${allClaimListResp?.length}</b> Claims?`)
          }
        }
      }
      )
    error => {
      // this.error = error;
      console.log(error);
      this.spinner.hide();
    };
  }

  allClaimListDispatchAlert(msg: string) {
    SwalFire.confirmCancelSwlAlert(msg).
      then((result) => {
        if (result.isConfirmed) {
          this.selectedGridItems = this.globalCheckboxSelectedGridItems;
          this.dispatchAcceptedClaims()
        }
        else if (result.isDenied) {
        }
      })
  }


  GridSortModel(params: any) {
    if (params.sortModel.length > 0) {
      const firstSort = params.sortModel[0];
      const agGridColId = firstSort.colId;
      const sortDirection = firstSort.sort; // 'asc' or 'desc'
      let backendSortingColumn: string = agGridColId; // Default to AG Grid's column ID
      // Use a switch statement to map specific AG Grid column IDs to backend fields
      switch (agGridColId) {
        case 'memberFullName':
          backendSortingColumn = 'MemFirstName';
          break;
        case 'renderingProviderFullName':
          backendSortingColumn = 'RenderingProviderFirstName';
          break;
        case 'dos':
          backendSortingColumn = 'DOSFrom';
          break;
        // Add more cases here for other columns if needed
        // case 'someOtherAgGridCol':
        //   backendSortingColumn = 'someOtherBackendField';
        //   break;
      }

      // Assign the determined backend sorting column and order
      this.dashboardSerchCriteria.sortingColumn = backendSortingColumn;
      this.dashboardSerchCriteria.sortOrder = sortDirection;
    }
  }

  gridPayloadRequest(params: any) {
    this.dashboardSerchCriteria.index = params.startRow;
  }

  onSelectionChanged(event: any) {
    {
      this.selectedGridItems = [];
      const selectedNodes = event.api.getSelectedNodes();
      const selectedData = selectedNodes.map(node => node.data);
      console.log('Currently Selected Rows (visible/loaded):', selectedData);
      if (this.buttonVisibleType == CLAIM_TYPE.accepted) {
        const isGloballySelected = selectedData.filter(node => node?.isGloballySelected == true);
        this.isAllSelectedGlobally = isGloballySelected.length > 0 ? true : false;
      }

      this.gridChangePushArrays(selectedData)
      this.selectedRow = selectedData;
    }
  }

  gridChangePushArrays(selectedRows: any) {
    let claimsList: any = []
    switch (this.buttonVisibleType) {
      case CLAIM_TYPE.open:
      case CLAIM_TYPE.accepted:
      case CLAIM_TYPE.dispatched:
      case CLAIM_TYPE.rejectedByCH:
      case CLAIM_TYPE.acceptedByCH:
      case CLAIM_TYPE.acknolwedgedByCH:
      case CLAIM_TYPE.unAckByPayer:
      case CLAIM_TYPE.rejectedByPayer:
      case CLAIM_TYPE.acknolwedgedByPayer:
      case CLAIM_TYPE.acceptedByPayer:
      case CLAIM_TYPE.pending:
      case CLAIM_TYPE.eobReceived:
      case CLAIM_TYPE.deniedByPayer: {
        selectedRows.forEach(selectedRowItem => {
          // if (selectedRowItem.patientCtrlNo>0) {
          this.selectedGridItems.push(selectedRowItem)
          let claimsStatus: ClaimStatusChangeModel = {
            claimId: selectedRowItem.claimId,
            //encounterNumber: "2",
            patientControlNumber: selectedRowItem.claimControlNumber,
          }
          claimsList.push(claimsStatus);
          //  }
        });
        let claimLst: ClaimStatusModel = {
          claimsStatus: claimsList,
          status: CLAIM_TYPE.accepted
        }
        this.claimsStatusList = claimLst;
        break;
      }

    }
  }


  selectedIPACode(event: any) {
    this.selectedIPACodes = '';
    event.forEach((element: any) => {
      this.selectedIPACodes += this.selectedIPACodes + ',' + element.mdmCode;
    });
  }

  onGridSizeChanged(e) {
    this.gridOptions.api.sizeColumnsToFit();
  }

  generateDispatchClaims(): void {
    if (this.isAllSelectedGlobally) {
      this.fetchAllClaimsListForGlobalCheckbox();
    }
    else {
      this.dispatchAcceptedClaims();
    }
  }
  // select row or de select row
  public isRowSelectable: IsRowSelectable = (
    params: any
  ) => {
    ;
    return ShowHideCheckboxClaimsGrid.showHideCheckbox(params) ? true : false;
  };
  refreshGrid() {
    if (this.gridApi) {
      //this.currentTabTotalFileCount = this.tabData.count- this.tabData.additionalCount;
      // Reset loading state when refreshing grid
      this.isGridLoading = false;
      this.onGridReady(this.gridApi);
    }

  }
  moveToAcceptedClaim() {
    this.claimsStatusList.lastModifiedFirstName = JSON.parse(localStorage.getItem('userFirstName')),
      this.claimsStatusList.lastModifiedLastName = JSON.parse(localStorage.getItem('userLastName')),
      this.claimsStatusList.lastModifiedMiddleName = JSON.parse(localStorage.getItem('userMiddleName')),

      this.claimService.claimStatusChange(this.claimsStatusList).subscribe((res: any) => {
        if (res) {
          this.notificationService.showSuccess('Selected claims are moved successfully to Accepted Tab.', ValidationMsgs.success, 4000);
          this.selectedGridItems = [];
          this.bucketCountRefresh();
          this.refreshGrid();

        }
      },
        error => {
          // this.error = error;
          // this.loading = false;
          this.spinner.hide();
        });

  }

  bindDOSDate() {
    this.claims?.forEach((item: any) => {
      let startDate;
      let endDate;
      if (item.dos) {
        if (item.dosFrom) {
          startDate = moment(item.dosFrom).format('MM-DD-YYYY');
        }
        if (item.dosTo) {
          endDate = moment(item.dosTo).format('MM-DD-YYYY');
        }
        if (startDate && endDate) {
          item.dos = startDate + ' - ' + endDate;
        }
        else if (startDate && !endDate) {
          item.dos = startDate + ' - ' + '';
        }
        else if (!startDate && !endDate) {
          item.dos = ''
        }
      }
    })
  }

  bindReasonForRejctecByCH() {
    this.claims?.forEach((item: any) => {
      if (item.claimStatusCode == CLAIM_TYPE.rejectedByCH) {
        if (item.fileType) {
          if (item.fileType == COMMON_VALUES.fileType) {
            item.statusReason = COMMON_VALUES._999;
            item.statusModifiedDate = item._999ProcessedOn_CH;
          }
          else {
            item.statusReason = COMMON_VALUES._277
            item.statusModifiedDate = item._277ProcessedOn_CH;
          }
        }
        else {
          item.statusReason = null;
          item.statusModifiedDate = null;
        }
      }
    })
  }

  dispatchAcceptedClaims() {

    this.selectedGridItems.forEach(item => item.fileType = item.claimType);
    this.dispatchClaimService.dispatchAcceptedClaims(this.selectedGridItems)
      .subscribe((data: any) => {
        if (data.message == 'Claim dispatch done. Please check after some time') {
          this.notificationService.showSuccess('Please check back in the 837 File List after some time.', `Claims Have been moved for Generation which are mapped to Clearing House.`, 4000);
        }
        else if (data.message == 'No Claim found for dispatch') {

          this.notificationService.showSuccess('', `There are no Claims for Generation`, 4000);

        } else if (data.message == 'Please wait for sometime') {
          this.notificationService.showSuccess('HOLD ON: DATE CHANGE AWAITED', `Clearing house may not accept the claims if there is a mismatch between received date and dispatched date. Please try again after 16 mins.`, 4000);
        } else if (data.message.includes('Please wait for sometime')) {
          let time = data.message.split('Please wait for sometime')[1];
          this.notificationService.showWarning(`Clearing house may not accept the claims if there is a mismatch between received date and dispatched date. Please try again after ` + time + ` mins.`, 'HOLD ON: DATE CHANGE AWAITED', 4000);
        }
        this.selectedGridItems = [];
        this.globalCheckboxSelectedGridItems = [];
        this.refreshGrid();
        this.subjectService.setFileRefreshAfterAdd(true);
        this.spinner.show();
      },
        error => {
          // this.error = error;
          // this.loading = false;
          this.spinner.hide();
        });

  }

  ignoreClaimCheckEvnt(ev: any) {
    if (ev.target.checked) {
      this.dashboardSerchCriteria.ignoredClaims = true;
    }
    else {
      this.dashboardSerchCriteria.ignoredClaims = false;
    }
    this.refreshGrid()
  }
  ignoreClaims() {
    this.claimsTrackingService.getIgnoreClaims(this.dashboardSerchCriteria)
      .subscribe((data: any) => {
        this.claimGridModel = data?.content?.content;
      },
        error => {

        });
    this.selectedGridItems = [];
  }

  subjectRefresh() {
    this.subjectService.getTabClaimInfo().pipe(first()).subscribe((data: any) => {
      if (data) {
        this.dashboardSerchCriteria = _.cloneDeep(data.dashboardSerchCriteria);
        this.dashboardSerchCriteria.sortOrder = null;
        this.dashboardSerchCriteria.sortingColumn = null;
        this.getCurrentTabGridColumnDef(this.dashboardSerchCriteria.claimFormStatusCode);
        //this.refreshGrid();
        this.claimCount = data.dashboardSerchCriteria.tempCount;
        this.currentTabTotalFileCount = data.dashboardSerchCriteria.tempCount;
        this.subjectService.resetTabClaimInfo();
        if (this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.rejectedByPayer || this.dashboardSerchCriteria.claimFormStatusCode == CLAIM_TYPE.rejectedByCH) {
          this.getUnattem();
        }
      }
    });


    this.subjectService.getBucketClaimGridRefresh().subscribe((isRefresh: boolean) => {
      if (isRefresh) {
        this.refreshGrid();
        this.selectedRow = [];
        this.subjectService.resetBucketClaimGridRefresh();
      }
    });
    // bucket count header refresh when click on Resubmit icon in Grid row.
    this.subjectService.getIsClaimMovedOtherBucket().pipe(takeUntil(this.destroy$)).subscribe((isRefresh: boolean) => {
      if (isRefresh) {
        this.claimCount = (Number(this.claimCount) - 1);
        this.subjectService.resetIsClaimMovedOtherBucket();
      }
    });

    this.subjectService.getDashboardBucketCountData().pipe(takeUntil(this.destroy$)).subscribe((dashboardData: any) => {
      if (!!dashboardData) {
        const dashboardObjectByStatusCode = dashboardData.find(claim => claim.statusCode == this.dashboardSerchCriteria.claimFormStatusCode)
        this.dashboardSerchCriteria.tempCount = dashboardObjectByStatusCode.totalClaims;
        this.currentTabTotalFileCount = dashboardObjectByStatusCode.totalClaims;
        this.claimCount = dashboardObjectByStatusCode.totalClaims;
        this.subjectService.resetDashboardBucketCountData();
      }
    });

  }

  reSubmit(): void {
    if (this.claimsStatusList.claimsStatus.length > 1) {
      let dialogOpen = this.dailog.open(ResubmitConfimComponent, {
        height: '230px',
        width: '500px',
        panelClass: 'custom-dialog-containers',
      })
      dialogOpen.afterClosed().subscribe(filterQuery => {
        if (filterQuery) {
          this.claimListArrayString = this.claimsStatusList.claimsStatus.filter(item => item.claimId).map(i => i.claimId).toString();
          this.reSubmitClaims();
        }
      })

    }
    else {
      this.claimListArrayString = this.claimsStatusList.claimsStatus.filter(item => item.claimId).map(i => i.claimId).toString();
      this.reSubmitClaims();
    }
    this.selectedGridItems = [];
  }

  reSubmitClaims(): void {
    this.claimReportService.resubmitClaim(this.claimListArrayString)
      .subscribe((item: any) => {
        if (item.statusCode == 200) {
          this.subjectService.setDashboardRefresh();
          this.refreshGrid();
          const claimId = item.message.split('-')[1];
          let msg = this.claimListArrayString.length > 1 ? `Claims ${claimId} created in the Resubmission tab.` : `Claim ${claimId} created in the Resubmission tab.`
          this.notificationService.showSuccess(msg,
            ValidationMsgs.success, 4000);
          this.selectedRow = [];


        }
      }, err => {
        this.notificationService.showError(`${this.claimListArrayString} Claim not Created`, `Error!`, 4000);
      });

  }


  bucketCountRefresh() {
    this.claimCount = (Number(this.claimCount) - this.claimsStatusList.claimsStatus.length);
    this.currentTabTotalFileCount = this.claimCount;
  }


  export() {
    let gridColumns = this.gridOptions.columnApi.getAllDisplayedColumns().filter(x => x.colDef.headerName != '').map(x => x.colDef)
    this.exportToExcel(gridColumns);
  }

  exportToExcel(selectedColumns: any) {
    const dataToDisplay = [];
    this.gridApi.forEachNodeAfterFilterAndSort((node) => {
      if (!!node && node.data) {
        dataToDisplay.push(node.data);
      }
    })
    let workbook = new Workbook();
    let worksheet = workbook.addWorksheet('search-claim');
    let columns = [];
    let numberOfColumnsToBeShown: number = 0;
    for (const colItem of selectedColumns) {
      if (colItem.headerName != '' && colItem.headerName != 'Action' && colItem.headerName != 'Select') {
        columns.push({ header: colItem.headerName, key: colItem.field, width: 25 });
        numberOfColumnsToBeShown = numberOfColumnsToBeShown + 1;
      }
    }
    worksheet.columns = columns;
    dataToDisplay.forEach(data => {
      if (!!data.dateCreated) {
        data.dateCreated = DateFormatter(data.dateCreated)
      };

      worksheet.addRow(data, "");
    });
    worksheet.getRow(1).font = {
      bold: true
    };
    worksheet.columns.forEach(column => {
      const lengths = column.values.map(v => v.toString().length);
      lengths.push(25);
      let maxLength = Math.max(...lengths.filter(v => typeof v === 'number'));
      if (maxLength > 400) {
        maxLength = 400;
      }
      column.width = maxLength;
    });
    worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: 1 }, { row: dataToDisplay.length + 1, col: numberOfColumnsToBeShown }, 'thin');
    worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: 1 }, { row: 1, col: numberOfColumnsToBeShown }, 'thin');
    for (let i = 1; i < numberOfColumnsToBeShown + 1; i++) {
      worksheet = this.exportService.addBorderToCells(worksheet, { row: 1, col: i }, { row: dataToDisplay.length + 1, col: i }, 'thin');
    }
    worksheet.getRow(1).eachCell(c => c.style.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: '6594EE' }
    })
    workbook.xlsx.writeBuffer().then((data) => {
      let blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      fs.saveAs(blob, 'open-claims');
    })
  }


  getPrivileges() {
    const privielagesDetails = this.globalService.getPrivilegesByRole();
    this.isClaimBillingMngmntGenrtEDI837File = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_GenerateEDI837File).length > 0 ? true : false;
    this.billerPrivMoveAllOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveAllOpenToAccepted_Biller).length > 0 ? true : false;
    this.privMoveAllOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveAllOpenToAccepted).length > 0 ? true : false
    this.isPrivMoveSelectedOpenToAccepted = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_MoveSelectedOpenToAccepted).length > 0 ? true : false
    this.isClaimsBillingManagementResubmission = privielagesDetails.privilegeCodes.filter(x => x == PREVILEGES.CLAIMS_BillingManagement_Resubmission).length > 0 ? true : false

  }
  unattendClaimsCount: number = 0;
  getUnattem() {
    this.claimReportService.getUnattendClaimsCount(this.dashboardSerchCriteria).pipe(takeUntil(this.destroy$))
      .subscribe((data: any) => {
        if (data.statusCode == 200) {
          this.unattendClaimsCount = data?.content;
        }
      });
  }

  ngOnDestroy() {
    if (this.gridApi) {
      this.gridApi = null;
    }
    this.destroy$.next(true);
    this.destroy$.unsubscribe();
  }
}

