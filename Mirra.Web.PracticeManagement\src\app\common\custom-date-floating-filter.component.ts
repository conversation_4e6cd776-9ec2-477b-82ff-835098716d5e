import { Component, ViewChild, ElementRef } from '@angular/core';
import { IFloatingFilter, IFloatingFilterParams, AgFrameworkComponent } from 'ag-grid-angular';

@Component({
  selector: 'app-custom-date-floating-filter',
  template: `
    <div>
      <input #input
             type="text"
             class="ag-input"
             [value]="currentValue"
             (input)="onInput($event)"
             placeholder="yyyy-mm-dd"
             style="width: 100%;" />
      <div *ngIf="errorMsg" style="color: red; font-size: 11px; margin-top:2px;">
        {{ errorMsg }}
      </div>
    </div>
  `
})
export class CustomDateFloatingFilterComponent implements IFloatingFilter, AgFrameworkComponent<IFloatingFilterParams> {
  @ViewChild('input', { static: true }) input!: ElementRef<HTMLInputElement>;
  params!: IFloatingFilterParams;
  currentValue: string = '';
  errorMsg: string = '';

  agInit(params: IFloatingFilterParams): void {
    this.params = params;
  }

  onInput(event: any) {
    const value = event.target.value;
    this.currentValue = value;
    this.errorMsg = '';

    // Check correct format yyyy-mm-dd
    if (value && !/^\d{4}-\d{2}-\d{2}$/.test(value)) {
      this.errorMsg = 'Format must be yyyy-mm-dd';
      this.sendValueToParent(null);
      return;
    }

    // Check year length and range
    if (value) {
      const year = value.split('-')[0];
      if (year.length !== 4 || Number(year) < 2010 || Number(year) > new Date().getFullYear()) {
        this.errorMsg = 'Year must be 4 digits, 2010 to current';
        this.sendValueToParent(null);
        return;
      }
      // Optionally, check if the whole date is valid
      const dateObj = new Date(value);
      if (isNaN(dateObj.getTime())) {
        this.errorMsg = 'Invalid date';
        this.sendValueToParent(null);
        return;
      }
    }

    this.sendValueToParent(value);
  }

  sendValueToParent(value: string | null) {
    this.params.parentFilterInstance((instance: any) => {
      instance.onFloatingFilterChanged('equals', value);
    });
  }

  onParentModelChanged(parentModel: any): void {
    // Not needed for basic validation
  }
}
