import { Component, ViewChild, ElementRef } from '@angular/core';
import { IFloatingFilter, IFloatingFilterParams } from 'ag-grid-community';
import { AgFrameworkComponent } from 'ag-grid-angular';

@Component({
  selector: 'app-custom-date-filter',
  template: `
    <div>
      <input #input
             type="text"
             class="ag-input"
             [(ngModel)]="currentValue"
             (input)="onInput($event)"
             (keydown)="onKeyDown($event)"
             placeholder="mm-dd-yyyy"
             style="width: 100%;" />
      <div *ngIf="errorMsg" style="color: red; font-size: 11px; margin-top:2px;">
        {{ errorMsg }}
      </div>
    </div>
  `
})
export class CustomDateFloatingFilterComponent implements IFloatingFilter, AgFrameworkComponent<IFloatingFilterParams> {
  @ViewChild('input', { static: true }) input!: ElementRef<HTMLInputElement>;
  params!: IFloatingFilterParams;
  currentValue: string = '';
  errorMsg: string = '';

  agInit(params: IFloatingFilterParams): void {
    this.params = params;
  }

  onKeyDown(event: KeyboardEvent) {
    // Allow backspace, delete, tab, escape, enter, and arrow keys
    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];
    if (allowedKeys.includes(event.key)) {
      return;
    }

    // Allow digits and hyphens for date format
    if (!/[\d\-]/.test(event.key)) {
      event.preventDefault();
    }
  }

  onInput(event: any) {
    const value = event.target.value;
    this.currentValue = value;
    this.errorMsg = '';

    // Check correct format mm-dd-yyyy
    if (value && !/^\d{2}\-\d{2}\-\d{4}$/.test(value)) {
      this.errorMsg = 'Format must be mm-dd-yyyy';
      this.sendValueToParent(null);
      return;
    }

    // Check year length and range
    if (value) {
      const parts = value.split('-');
      const month = parts[0];
      const day = parts[1];
      const year = parts[2];

      if (year.length !== 4 || Number(year) < 2010 || Number(year) > new Date().getFullYear()) {
        this.errorMsg = 'Year must be 4 digits, 2010 to current';
        this.sendValueToParent(null);
        return;
      }

      // Convert mm-dd-yyyy to yyyy-mm-dd for Date constructor
      const isoDateString = `${year}-${month}-${day}`;
      const dateObj = new Date(isoDateString);
      if (isNaN(dateObj.getTime())) {
        this.errorMsg = 'Invalid date';
        this.sendValueToParent(null);
        return;
      }
    }

    this.sendValueToParent(value);
  }

  sendValueToParent(value: string | null) {
    this.params.parentFilterInstance((instance: any) => {
      if (value && value.trim() !== '') {
        // Convert mm-dd-yyyy to Date object for ag-grid date filter
        const parts = value.split('-');
        if (parts.length === 3) {
          const month = parts[0];
          const day = parts[1];
          const year = parts[2];
          const isoDateString = `${year}-${month}-${day}`;
          const dateObj = new Date(isoDateString);

          if (!isNaN(dateObj.getTime())) {
            instance.onFloatingFilterChanged('equals', dateObj);
          } else {
            instance.onFloatingFilterChanged('equals', null);
          }
        } else {
          instance.onFloatingFilterChanged('equals', null);
        }
      } else {
        instance.onFloatingFilterChanged('equals', null);
      }
    });
  }

  onParentModelChanged(parentModel: any): void {
    // Update the floating filter when parent filter changes
    if (parentModel && parentModel.dateFrom) {
      const date = new Date(parentModel.dateFrom);
      if (!isNaN(date.getTime())) {
        // Format date as mm-dd-yyyy for display
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        this.currentValue = `${month}-${day}-${year}`;
      }
    } else {
      this.currentValue = '';
    }
    this.errorMsg = '';
  }
}
