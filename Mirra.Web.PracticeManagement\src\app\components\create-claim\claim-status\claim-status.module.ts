
import { CommonModule } from '@angular/common';
import { ClaimStatusComponent } from './claim-status.component';
import { AgGridModule } from 'ag-grid-angular';
import { NgModule } from "@angular/core";
import { MatTooltipModule } from '@angular/material/tooltip';
import { NgSelectModule } from '@ng-select/ng-select';
import { ReactiveFormsModule, FormsModule } from '@angular/forms';
import { DispatchClaimService } from 'src/app/services/claim-buckets/dispatch-claim.service';
import { ClaimsTrackingService } from 'src/app/services/claims-tracking/claims-tracking.service';
import { ResubmitClaimComponent } from '../../popups/resubmit-claim/resubmit-claim.component';
import { MatDialogModule } from '@angular/material/dialog';
import { NotificationService } from 'src/app/services/Notification/notification.service';
import { ClaimReportServiceService } from 'src/app/services/Dashboard/claim-report-service.service';
import { IgnoreClaimComponent } from '../../popups/ignore-claim/ignore-claim.component';
import { ResubmitConfimComponent } from '../../popups/resubmit-confim/resubmit-confim.component';
import { RejectedByCHActionRenderer } from 'src/app/shared/Renderer/claim-grid/RejectedByCHActionRenderer';
import { AcceptedClaimActionRenderer } from 'src/app/shared/Renderer/claim-grid/AcceptedClaimActionRenderer';
import { AcceptedClaimLockerActionRenderer } from 'src/app/shared/Renderer/claim-grid/AcceptedClaimLockerActionRenderer';
import { ClaimService } from 'src/app/services/ClaimForm/claim.service';
import { ResubmissionExclaimarkRenderer } from 'src/app/shared/Renderer/claim-grid/ResubmissionExclaimarkRenderer';
import { DispatchedResubmsnExclaimarkRenderer } from 'src/app/shared/Renderer/claim-grid/DispatchedResubmsnExclaimarkRenderer';
import { OnHoldReasonComponent } from '../../popups/dashboard-popups/on-hold-reason/on-hold-reason.component';
import { OnHoldReasonActionRenderer } from 'src/app/shared/Renderer/claim-grid/OnHoldReasonActionRenderer';
import { PendingResubmissionExclaimarkRenderer } from 'src/app/shared/Renderer/claim-grid/PendingResubmissionExclaimarkRenderer';
import { PopoverModule } from 'ngx-bootstrap/popover';
import { EobReportsReasonRenderer } from 'src/app/shared/Renderer/claim-grid/EobReportsReasonRenderer';
import { ValidateAddressComponent } from '../../popups/validate-address/validate-address.component';
import { UnAckByPayerActionRenderer } from 'src/app/shared/Renderer/claim-grid/UnAckByPayerActionRenderer';
import { DispatchedClaimActionRender } from 'src/app/shared/Renderer/claim-grid/DispatchedClaimActionRenderer';
import { EobRecievedClaimActionRenderer } from 'src/app/shared/Renderer/claim-grid/EOBRecievedClaimActionRenderer';
import { OpenClaimActionRender } from 'src/app/shared/Renderer/claim-grid/OpenClaimActionRenderer';
import { UnAckByCHClaimActionRenderer } from 'src/app/shared/Renderer/claim-grid/UnAckByCHClaimActionRenderer';
import { CustomDateFloatingFilterComponent } from 'src/app/common/custom-date-floating-filter.component';
// Ensure the path is correct; adjust if necessary

@NgModule({
  declarations: [
    ClaimStatusComponent,
    ResubmitClaimComponent,
    IgnoreClaimComponent,
    ValidateAddressComponent,
    ResubmitConfimComponent,
    OnHoldReasonComponent,
    AcceptedClaimActionRenderer,
    RejectedByCHActionRenderer,
    ResubmissionExclaimarkRenderer,
    AcceptedClaimLockerActionRenderer,
    DispatchedResubmsnExclaimarkRenderer,
    OnHoldReasonActionRenderer,
    PendingResubmissionExclaimarkRenderer,
    EobReportsReasonRenderer,
    UnAckByPayerActionRenderer,
    DispatchedClaimActionRender,
    EobRecievedClaimActionRenderer,
    OpenClaimActionRender,
    UnAckByCHClaimActionRenderer,
    CustomDateFloatingFilterComponent
  ],
  imports: [
    ReactiveFormsModule,
    FormsModule,
    CommonModule,
    AgGridModule,
    NgSelectModule,
    MatTooltipModule,
    MatDialogModule,
    PopoverModule
  ], schemas: [],
  providers: [
    DispatchClaimService, ClaimsTrackingService, NotificationService, ClaimReportServiceService, ClaimService
  ],
  exports: [ClaimStatusComponent]
})
export class ClaimStatusModule { }
