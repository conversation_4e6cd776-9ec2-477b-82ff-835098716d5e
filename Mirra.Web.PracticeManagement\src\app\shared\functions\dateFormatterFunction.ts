import * as moment from "moment";

export function DateFormatter(value) {
    return !!value ? moment(new Date(value)).format('MM-DD-YYYY') : '';
}
export function DateFormatterWithTime(value) {
    return !!value ? moment(new Date(value)).format('MM-DD-YYYY HH:mm:ss') : '';
}

export function getInDays(value) {
    if(value){

        var selectedDate =moment((new Date(value)).setHours(0, 0, 0, 0));
        var currentDate = moment((new Date()).setHours(0, 0, 0, 0));
      
        var result =   currentDate.diff(selectedDate, 'days');
      return result;
    }
    else{
       return  '';
    }
}

export function NameFormatter(data, field1, field2) {
    let result = [];
    if (!!data[field1]) {
        result.push(data[field1])
    }
    if (!!data[field2]) {
        result.push(data[field2])
    }
    return result.join(' ');
}
export function SortAgeValues(valueA, valueB) {
    if(!valueA && !valueB){
        return 0;
    }
    if(!valueA){
        return -1;
    }
    if(!valueB){
        return 1
    }
    if (getInDays(valueA) > getInDays(valueB)) {
        return 1;
    }
    if (getInDays(valueA) == getInDays(valueB)) {
        return 0;
    }
    if (getInDays(valueA) < getInDays(valueB)) {
        return -1;
    }
    return 0;
}