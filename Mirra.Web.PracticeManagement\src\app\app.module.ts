import { APP_INITIALIZER, NgModule } from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { EnvServiceProvider } from './shared/services/env/env.service.provider';
import { AppInitService } from './shared/services/envappconfig/app-init.service';
import { NgxSpinnerModule } from 'ngx-spinner';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CommonModule, DatePipe } from '@angular/common';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { GlobalService } from './shared/services/global.service';
import { HTTP_INTERCEPTORS, HttpClientModule } from '@angular/common/http';  
import { AgGridModule } from 'ag-grid-angular';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { BsDatepickerModule } from 'ngx-bootstrap/datepicker';
import { AvatarModule } from 'ngx-avatars';
import { ProcessRemitComponent } from './components/process-remit/process-remit.component';
import { RemitWorklistComponent } from './components/remit-worklist/remit-worklist.component';
import { MenuListItemComponent } from './components/menu-list-item/menu-list-item.component';
import { FfsComponent } from './components/create-claim/ffs/ffs.component';
import { CapComponent } from './components/create-claim/cap/cap.component';  
import { AppointmentScheduleComponent } from './components/Patient-Scheduling/appointment-schedule/appointment-schedule.component';
import { RemitProcessService } from './services/remit-process/remit-process.service';
import { NewAppointmentPopupComponent } from './components/popups/patient-scheduling/new-appointment-popup/new-appointment-popup.component';
import { NewPatientPopupComponent } from './components/popups/patient-scheduling/new-patient-popup/new-patient-popup.component';
import { NgSelectModule } from '@ng-select/ng-select';
import { AutocompleteLibModule } from 'angular-ng-autocomplete';
import { SearchResultPopupComponent } from './components/popups/patient-scheduling/search-result-popup/search-result-popup.component';
import { FilterPopupComponent } from './components/popups/dashboard-popups/filter-popup/filter-popup.component';
import { BillingProviderPopupComponent } from './components/popups/billing-provider-popup/billing-provider-popup.component';
import { RenderingProviderPopupComponent } from './components/rendering-provider-popup/rendering-provider-popup.component';
import { FullCalendarModule } from '@fullcalendar/angular';
import { AssignRemitComponent } from './components/remit-worklist/assign-remit/assign-remit.component';
import { EditAppointmentPopupComponent } from './components/popups/patient-scheduling/edit-appointment-popup/edit-appointment-popup.component';
import { ToastrModule } from 'ngx-toastr';
import { NewClaimComponent } from './components/New-Claim/new-claim.component';
import { UserAuthenticationService } from './services/UserAuthentication/UserAuthentication.service';
import { HashLocationStrategy, LocationStrategy } from '@angular/common';
import { SaveModalComponent } from './modals/save-modal/save-modal.component';
import { AddPatientComponent } from './components/patient/add-patient/add-patient.component';
import { FilesComponent } from './components/files/files.component';
import {ProfessionalClaimsComponentComponent} from './components/files/professional-claims-component/professional-claims-component.component';
import { AckFiles999Component } from './components/files/ack-files999.component/ack-files999.component.component';
import { AckFiles997Component } from './components/files/ack-files997.component/ack-files997.component.component';
import { ClaimAckReport277Component } from './components/files/claim-ack-report277.component/claim-ack-report277.component.component';
import{Received835FilesComponent} from'./components/files/era835.component/era835.component.component';
// import { TableModule } from 'primeng/table';
import { SidebarModule } from './components/side-bar/side-bar.module';
import { SanitizeUrlPipe } from './shared/pipes/sanitize-url.pipe';
import { FilePreviewDialogComponent } from './shared/components/file-preview-dialog/file-preview-dialog.component';
import { DateFormatService } from './shared/services/dateformat';
import { ClaimStatusModule } from './components/create-claim/claim-status/claim-status.module';
import { DashboardModule } from './components/dashboard/dashboard.module';
import { RendererModule } from './shared/Renderer/renderer.module';
import { MembersModule } from './components/members/members.module';
import { FileModule } from './components/file/file.module';
import { AppMaterialModule } from './material-module';
import { MatRangeDatePickerModule } from './shared/components/form-inputs/mat-range-date-picker/mat-range-date-picker.module';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { CustomDateAdapter, MY_DATE_FORMAT } from './common/custom-date-adapter';
import { ProvidersModule } from './components/providers/providers.module';
import { UpdateClaimComponent } from './modals/update-claim/update-claim.component';
import { ClaimCreationModule } from './components/create-claim/claim-creation/claim-creation.module';
import { AddressMismatchComponent } from './shared/components/address-mismatch/address-mismatch.component';
import { LoaderInterceptor } from './shared/interceptor/loader.interceptor';
import { NetworkErrorInterceptor } from './shared/interceptor/network-error.interceptor';
import { SpinnerComponent } from './shared/components/spinner/spinner.component';
import { DublicateClaimComponent } from './modals/dublicate-claim/dublicate-claim.component';
import { AuthInterceptor } from './shared/interceptor/auth.interceptor';
import { NgIdleKeepaliveModule } from '@ng-idle/keepalive';

import { PopoverModule } from 'ngx-bootstrap/popover';
import { SubjectService } from './shared/services/subject.service';
import { ForgotPasswordComponent } from './components/forgot-password/forgot-password.component';
import { ResetPasswordComponent } from './components/reset-password/reset-password.component';
import { ChangePasswordComponent } from './components/change-password/change-password.component';
import { MatDialogModule } from '@angular/material/dialog';
import { ReportsModule } from './components/reports/reports.module';
import { InputDirectivesModule } from './shared/directives/input-directives.module';
import { LoginModule } from './components/login/login.module';
import { PowerBIModule } from './components/power-bi-reports/power-bi-reports.module';
import { SearchClaimModule } from './components/search-claim/search-claim.module';
import { MatMomentDateModule } from '@angular/material-moment-adapter';
import { InsuranceModule } from './components/insurance/insurance.module';
import { ConfirmMessagePopupComponent } from './modals/confirm-message-popup/confirm-message-popup.component';
import { CacheService } from './services/cache-service/cache.service';
import { DisplayUpperSaveLowerDirective } from './shared/directives/display-upper-save-lower-directive';
import { SelectAllComponent } from './shared/components/select-all/select-all.component';
import { CustomDateFloatingFilterComponent } from './common/custom-date-floating-filter.component';
export function init_app(appLoadService: AppInitService) {
  return () => appLoadService.init();
}
 

@NgModule({
  declarations: [
    AppComponent,   
    ProcessRemitComponent,
    RemitWorklistComponent,
    MenuListItemComponent,
    FfsComponent,
    CapComponent, 
    FilterPopupComponent,
    AppointmentScheduleComponent,
    NewAppointmentPopupComponent,
    EditAppointmentPopupComponent,
    NewPatientPopupComponent,
    SearchResultPopupComponent,
    BillingProviderPopupComponent,
    RenderingProviderPopupComponent,
    AssignRemitComponent,
    NewClaimComponent,
    SaveModalComponent,
    DublicateClaimComponent,
    ForgotPasswordComponent,
    ResetPasswordComponent,
    UpdateClaimComponent,
    AddPatientComponent,
    FilesComponent,
    ProfessionalClaimsComponentComponent,
    AckFiles999Component,
    AckFiles997Component,
    ClaimAckReport277Component,
    Received835FilesComponent,
    SanitizeUrlPipe,
    FilePreviewDialogComponent,
    AddressMismatchComponent,
    SpinnerComponent,
    ChangePasswordComponent,
    ConfirmMessagePopupComponent,
    DisplayUpperSaveLowerDirective  ,
    SelectAllComponent,
    CustomDateFloatingFilterComponent
  ],
  imports: [
    BrowserModule, 
    BrowserAnimationsModule,
    CommonModule,
    AppRoutingModule,
    ReactiveFormsModule,
    HttpClientModule,
    FormsModule,
    AgGridModule,
    DragDropModule,
    NgxSpinnerModule.forRoot(),
    AvatarModule,
    FullCalendarModule,
    NgSelectModule,
    AutocompleteLibModule,
    BsDatepickerModule.forRoot(),
    ToastrModule.forRoot({
      toastClass: 'ngx-toastr custom-toast-width',
      preventDuplicates: true,
      progressBar: true,
      positionClass: 'toast-top-right',
    }),
    ClaimStatusModule,
    RendererModule,
    MembersModule,
    FileModule,
    AppMaterialModule,
    MatRangeDatePickerModule,
    LoginModule,
    DashboardModule,
    ProvidersModule,
    PopoverModule.forRoot(),
    NgIdleKeepaliveModule.forRoot(),
    SidebarModule,
    MatDialogModule,
    ReportsModule,
    PowerBIModule,
    InputDirectivesModule,
    SearchClaimModule,
    MatMomentDateModule,
    InsuranceModule
    
  ],
  providers: [GlobalService, EnvServiceProvider,SubjectService,
    { provide: LocationStrategy, useClass: HashLocationStrategy },
    AppInitService, RemitProcessService, UserAuthenticationService, DatePipe,DateFormatService,CacheService,
    {
      provide: APP_INITIALIZER,
      useFactory: init_app,
      deps: [AppInitService],
      multi: true
    },
     {
      provide: DateAdapter,
      useClass: CustomDateAdapter,
      deps: [MAT_DATE_LOCALE] /*MomentDateAdapter*/
    },
    { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMAT },
    { provide: HTTP_INTERCEPTORS, useClass: LoaderInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: NetworkErrorInterceptor, multi: true },
    { provide: HTTP_INTERCEPTORS, useClass: AuthInterceptor, multi: true } 
  ],
  bootstrap: [AppComponent]
})
export class AppModule {
}
